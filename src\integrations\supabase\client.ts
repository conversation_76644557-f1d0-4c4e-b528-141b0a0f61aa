// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://iwzkguwkirrojxewsoqc.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3emtndXdraXJyb2p4ZXdzb3FjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3ODUxMDYsImV4cCI6MjA2ODM2MTEwNn0.Rui4W-MwszWqUSxX8DwktonhEnrsRMQhM-YkedNlY5Q";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});