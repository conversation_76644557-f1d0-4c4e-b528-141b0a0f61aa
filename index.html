
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FinanceTracker - Master Your Financial Future</title>
    <meta name="description" content="Take control of your finances with AI-powered insights, smart budgeting, and personalized recommendations." />
    <meta name="author" content="FinanceTracker" />
    
    <!-- Favicon -->
    <link rel="icon" href="/lovable-uploads/************************************.png" type="image/png">
    <link rel="shortcut icon" href="/lovable-uploads/************************************.png" type="image/png">
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="/lovable-uploads/************************************.png">
    <link rel="apple-touch-icon" sizes="57x57" href="/lovable-uploads/************************************.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/lovable-uploads/************************************.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/lovable-uploads/************************************.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/lovable-uploads/************************************.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/lovable-uploads/************************************.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/lovable-uploads/************************************.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/lovable-uploads/************************************.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/lovable-uploads/************************************.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/lovable-uploads/************************************.png">

    <!-- Browser Pinned Tab Icon -->
    <link rel="mask-icon" href="/lovable-uploads/************************************.png" color="#5bbad5">

    <!-- PWA Web App Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- Theme colors -->
    <meta name="theme-color" content="#4F46E5">
    <meta name="msapplication-TileColor" content="#4F46E5">
    <meta name="msapplication-TileImage" content="/lovable-uploads/************************************.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Open Graph / Social Media -->
    <meta property="og:title" content="FinanceTracker - Master Your Financial Future" />
    <meta property="og:description" content="Take control of your finances with AI-powered insights, smart budgeting, and personalized recommendations." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/lovable-uploads/************************************.png" />
    <meta property="og:url" content="https://financetracker.app" />
    <meta property="og:site_name" content="FinanceTracker" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@financetracker" />
    <meta name="twitter:title" content="FinanceTracker - Master Your Financial Future" />
    <meta name="twitter:description" content="Take control of your finances with AI-powered insights, smart budgeting, and personalized recommendations." />
    <meta name="twitter:image" content="/lovable-uploads/************************************.png" />

    <!-- Additional PWA meta tags -->
    <meta name="application-name" content="FinanceTracker">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="FinanceTracker">
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
